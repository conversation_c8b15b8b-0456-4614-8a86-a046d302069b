# 🧠 Smart Study Assistant - Complete Project Documentation

## 📋 Table of Contents
1. [Project Overview](#project-overview)
2. [Architecture & Tech Stack](#architecture--tech-stack)
3. [Project Structure](#project-structure)
4. [File-by-File Analysis](#file-by-file-analysis)
5. [Setup & Installation](#setup--installation)
6. [API Documentation](#api-documentation)
7. [Usage Commands](#usage-commands)
8. [Development Workflow](#development-workflow)
9. [Testing](#testing)
10. [Configuration](#configuration)
11. [Future Roadmap](#future-roadmap)

---

## 🎯 Project Overview

**Smart Study Assistant** is an AI-powered educational tool that transforms study materials into interactive learning experiences. Built with **Retrieval-Augmented Generation (RAG)**, **structured outputs**, and **function calling**, it helps students create flashcards, quizzes, and get instant answers from their documents.

### Key Features
- 🃏 **AI Flashcard Generation** - Convert any topic into study flashcards
- 📝 **Quiz Generation** - Create multiple-choice questions (planned)
- 🔍 **Document Q&A** - Ask questions about uploaded PDFs (planned)
- ⏰ **Study Reminders** - Schedule study sessions (planned)
- 🎯 **Structured Outputs** - JSON-formatted responses for consistency
- 🧠 **Context-Aware** - Uses RAG for document-grounded responses

---

## 🏗️ Architecture & Tech Stack

### Backend Stack
- **Framework**: FastAPI (Python 3.10+)
- **AI/LLM**: OpenAI GPT-4.1-mini
- **Embeddings**: OpenAI text-embedding-3-small
- **Vector Database**: ChromaDB + FAISS (optional)
- **Document Processing**: PyMuPDF for PDF parsing
- **Configuration**: Pydantic Settings with .env support

### AI Components
- **Prompt Engineering**: RTFC framework (Role, Task, Format, Context)
- **RAG Pipeline**: Document chunking → Embeddings → Vector search → Context injection
- **Structured Outputs**: JSON schemas for flashcards, quizzes, and responses
- **Function Calling**: Planned for reminders and external integrations

---

## 📁 Project Structure

```
Smart-Study-Assistant/
├── 📁 src/                          # Main source code
│   ├── 📁 core/                     # Core AI and infrastructure
│   │   ├── ai_client.py             # OpenAI client wrapper
│   │   ├── embeddings.py            # Text embedding utilities
│   │   ├── prompts.py               # Prompt templates & factory
│   │   ├── vector_db.py             # ChromaDB operations
│   │   └── schemas.py               # Pydantic data models
│   ├── 📁 features/                 # Main application features
│   │   ├── flashcard_generator.py   # ✅ AI flashcard generation
│   │   ├── question_answering.py    # 🚧 RAG-based Q&A (planned)
│   │   ├── quiz_generator.py        # 🚧 MCQ generation (planned)
│   │   └── reminder_system.py       # 🚧 Study reminders (planned)
│   ├── 📁 utils/                    # Utility functions
│   │   ├── document_processor.py    # PDF text extraction
│   │   ├── similarity_metrics.py    # Vector similarity functions
│   │   └── token_counter.py         # Token usage tracking
│   ├── 📁 evaluation/               # Testing & evaluation
│   │   ├── test_dataset.py          # Test cases and datasets
│   │   ├── judge_prompts.py         # AI evaluation prompts
│   │   └── testing_framework.py     # Automated testing
│   └── app.py                       # 🚀 FastAPI application entry point
├── 📁 config/                       # Configuration files
│   └── config.py                    # Settings and environment variables
├── 📁 data/                         # Data storage
│   └── sample_documents/            # Sample PDFs and documents
├── 📁 tests/                        # Unit and integration tests
│   └── test_sample.py               # Sample test cases
├── 📄 requirements.txt              # Python dependencies
├── 📄 pyproject.toml               # Project metadata and build config
├── 📄 .env                         # Environment variables (API keys)
├── 📄 .gitignore                   # Git ignore patterns
├── 📄 README.md                    # Project overview
├── 📄 AI_CHATBOT_IMPLEMENTATION_GUIDE.md  # Implementation guide
└── 📄 PROJECT_DOCUMENTATION.md     # This comprehensive documentation
```

### Status Legend
- ✅ **Implemented & Working**
- 🚧 **Planned/In Development**
- 📄 **Documentation**
- 📁 **Directory**

---

## 📂 File-by-File Analysis

### 🚀 Core Application Files

#### `src/app.py` - FastAPI Application
**Purpose**: Main API server with endpoints for flashcard generation
**Key Components**:
- FastAPI app initialization
- Health check endpoint (`/health`)
- Flashcard generation endpoints (`GET/POST /flashcards`)
- Request/Response models using Pydantic
- Error handling and HTTP status codes

**Endpoints**:
```python
GET  /health           # Server health and model info
GET  /flashcards       # API usage information
POST /flashcards       # Generate flashcards from topic
```

#### `config/config.py` - Configuration Management
**Purpose**: Centralized configuration using Pydantic Settings
**Features**:
- Environment variable loading from `.env`
- OpenAI API key management
- Model configuration (chat & embedding models)
- Server settings (host, port)
- Vector database directory configuration

### 🧠 Core AI Components

#### `src/core/ai_client.py` - OpenAI Client Wrapper
**Purpose**: Abstraction layer for OpenAI API calls
**Features**:
- Configurable model parameters (temperature, top_p, max_tokens)
- Chat completion interface
- Error handling and response parsing
- Support for custom stop sequences

#### `src/core/prompts.py` - Prompt Engineering Framework
**Purpose**: RTFC (Role, Task, Format, Context) prompt templates
**Key Methods**:
- `zero_shot()` - Basic question answering
- `one_shot()` - Single example prompting
- `multi_shot()` - Multiple example prompting
- `chain_of_thought()` - Step-by-step reasoning
- `structured_flashcards()` - JSON flashcard generation
- `structured_quiz()` - JSON quiz generation
- `select_prompt()` - Automatic prompt selection based on user input

#### `src/core/embeddings.py` - Text Embeddings
**Purpose**: Generate vector embeddings for text similarity
**Features**:
- OpenAI embedding model integration
- Batch text processing
- Vector similarity calculations

#### `src/core/vector_db.py` - Vector Database Operations
**Purpose**: ChromaDB integration for document storage and retrieval
**Features**:
- Persistent vector storage
- Document embedding and indexing
- Similarity search and retrieval
- Metadata management

### 🎯 Feature Implementations

#### `src/features/flashcard_generator.py` - Flashcard Generation ✅
**Purpose**: AI-powered flashcard creation from topics and context
**Key Features**:
- Topic-based flashcard generation
- Optional context integration
- JSON response parsing with markdown cleanup
- Fallback handling for malformed responses
- Configurable flashcard count

**Usage Example**:
```python
generator = FlashcardGenerator(ai_client)
result = generator.generate_flashcards(
    topic="photosynthesis", 
    context="optional context text",
    count=5
)
```

#### `src/features/question_answering.py` - RAG Q&A 🚧
**Status**: Planned for Phase 4
**Purpose**: Document-based question answering using RAG

#### `src/features/quiz_generator.py` - Quiz Generation 🚧
**Status**: Planned for Phase 3
**Purpose**: Multiple-choice question generation

#### `src/features/reminder_system.py` - Study Reminders 🚧
**Status**: Planned for Phase 5
**Purpose**: Function calling for study session scheduling

### 🛠️ Utility Functions

#### `src/utils/document_processor.py` - Document Processing
**Purpose**: Extract text from PDF documents
**Features**:
- PyMuPDF integration for PDF parsing
- Text extraction and cleaning
- Support for multi-page documents

#### `src/utils/similarity_metrics.py` - Vector Similarity 🚧
**Purpose**: Calculate similarity between text embeddings

#### `src/utils/token_counter.py` - Token Usage Tracking 🚧
**Purpose**: Monitor and log API token consumption

### 🧪 Testing & Evaluation

#### `tests/test_sample.py` - Sample Tests
**Purpose**: Basic functionality testing
**Features**:
- AI client testing
- Prompt factory validation
- Flashcard generation testing

#### Helper Scripts
- `test_api.py` - API endpoint testing script
- `debug_flashcards.py` - Flashcard generation debugging
- `test_direct_ai.py` - Direct AI client testing
- `run_tests.sh` - Test execution script

---

## ⚙️ Setup & Installation

### Prerequisites
- Python 3.10 or higher
- OpenAI API key
- Git

### Installation Steps

1. **Clone the Repository**
```bash
git clone https://github.com/kalviumcommunity/Smart-Study-Assistant.git
cd Smart-Study-Assistant
```

2. **Create Virtual Environment**
```bash
python -m venv .venv
# Windows
.venv\Scripts\activate
# Linux/Mac
source .venv/bin/activate
```

3. **Install Dependencies**
```bash
pip install -r requirements.txt
```

4. **Configure Environment Variables**
```bash
# Copy and edit .env file
cp .env.example .env
# Add your OpenAI API key to .env
```

5. **Start the Server**
```bash
uvicorn src.app:app --reload --host 0.0.0.0 --port 8000
```

### Dependencies Overview
```
Core Runtime:
- openai>=1.40.0              # OpenAI API client
- fastapi>=0.111.0            # Web framework
- uvicorn[standard]>=0.30.0   # ASGI server
- pydantic>=2.7.0             # Data validation
- pydantic-settings>=2.10.0   # Settings management
- python-dotenv>=1.0.1        # Environment variables

RAG & LLM:
- langchain>=0.2.0            # LLM orchestration
- langchain-openai>=0.1.7     # OpenAI integration

Vector Database:
- chromadb>=0.5.0             # Vector database
- faiss-cpu>=1.8.0            # Vector similarity search

Document Processing:
- pymupdf>=1.24.0             # PDF parsing

Utilities:
- numpy>=1.26.0               # Numerical operations
- tiktoken>=0.7.0             # Token counting

Testing:
- pytest>=8.2.0               # Testing framework
```

---

## 🌐 API Documentation

### Base URL
```
http://localhost:8000
```

### Endpoints

#### Health Check
```http
GET /health
```
**Response**:
```json
{
  "status": "ok",
  "model_chat": "gpt-4.1-mini",
  "model_embedding": "text-embedding-3-small"
}
```

#### Flashcard Generation Info
```http
GET /flashcards
```
**Response**:
```json
{
  "message": "Use POST method to generate flashcards",
  "endpoint": "/flashcards",
  "method": "POST",
  "example_payload": {
    "topic": "photosynthesis",
    "context": "optional context text",
    "count": 5
  }
}
```

#### Generate Flashcards
```http
POST /flashcards
Content-Type: application/json

{
  "topic": "photosynthesis",
  "context": "optional context text",
  "count": 3
}
```

**Response**:
```json
{
  "topic": "photosynthesis",
  "flashcards": [
    {
      "question": "What are the main stages of photosynthesis?",
      "answer": "Light-dependent reactions and Calvin cycle...",
      "source": null
    },
    {
      "question": "Where does photosynthesis occur?",
      "answer": "In chloroplasts of plant cells...",
      "source": null
    }
  ]
}
```

---

## 💻 Usage Commands

### Development Commands
```bash
# Start development server
uvicorn src.app:app --reload --host 0.0.0.0 --port 8000

# Run tests
python -m pytest tests/ -v
# OR
./run_tests.sh

# Run specific test
PYTHONPATH=. python tests/test_sample.py

# Test API endpoints
python test_api.py

# Debug flashcard generation
python debug_flashcards.py

# Test AI client directly
python test_direct_ai.py
```

### Production Commands
```bash
# Start production server
uvicorn src.app:app --host 0.0.0.0 --port 8000 --workers 4

# Install production dependencies only
pip install -r requirements.txt --no-dev
```

### Utility Commands
```bash
# Check Python path issues
PYTHONPATH=. python -c "from src.core.ai_client import AIClient; print('Import successful')"

# Test OpenAI connection
python -c "from config.config import settings; print('API Key configured:', bool(settings.OPENAI_API_KEY))"

# Format code (if using black)
black src/ tests/

# Type checking (if using mypy)
mypy src/
```

---

## 🔧 Development Workflow

### Phase-Based Development
The project follows a structured phase-based approach:

**Phase 1**: Foundation & Core Setup ✅
- Basic FastAPI application
- OpenAI client integration
- Configuration management
- Basic prompt templates

**Phase 2**: Embeddings & Vector DB 🚧
- Text embedding generation
- ChromaDB integration
- Document chunking and indexing

**Phase 3**: Structured Outputs ✅
- Flashcard generation (completed)
- Quiz generation (planned)
- JSON schema validation

**Phase 4**: RAG Implementation 🚧
- Document upload and processing
- Vector similarity search
- Context-aware question answering

**Phase 5**: Advanced Features 🚧
- Function calling for reminders
- Study session scheduling
- Performance analytics

### Git Workflow
```bash
# Feature development
git checkout -b feature/new-feature
git add .
git commit -m "Add new feature"
git push origin feature/new-feature

# Create pull request and merge to main
```

### Code Style Guidelines
- Use type hints for all functions
- Follow PEP 8 naming conventions
- Add docstrings to all classes and methods
- Keep functions focused and single-purpose
- Use Pydantic models for data validation

---

## 🧪 Testing

### Test Structure
```
tests/
├── test_sample.py          # Basic functionality tests
├── test_ai_client.py       # AI client unit tests (planned)
├── test_prompts.py         # Prompt template tests (planned)
├── test_flashcards.py      # Flashcard generation tests (planned)
└── test_api.py             # API endpoint tests (planned)
```

### Running Tests
```bash
# Run all tests
python -m pytest tests/ -v

# Run specific test file
python -m pytest tests/test_sample.py -v

# Run with coverage
python -m pytest tests/ --cov=src --cov-report=html

# Run tests with Python path
PYTHONPATH=. python tests/test_sample.py
```

### Test Categories
1. **Unit Tests**: Individual component testing
2. **Integration Tests**: API endpoint testing
3. **AI Tests**: LLM response validation
4. **Performance Tests**: Response time and token usage

---

## ⚙️ Configuration

### Environment Variables (.env)
```bash
# Required
OPENAI_API_KEY=your_openai_api_key_here

# Model Configuration
MODEL_CHAT=gpt-4.1-mini
MODEL_EMBEDDING=text-embedding-3-small

# Vector Database
VECTOR_DB_DIR=./chroma

# Server Configuration
HOST=0.0.0.0
PORT=8000
```

### Configuration Options
- **OPENAI_API_KEY**: Your OpenAI API key (required)
- **MODEL_CHAT**: Chat completion model (default: gpt-4.1-mini)
- **MODEL_EMBEDDING**: Embedding model (default: text-embedding-3-small)
- **VECTOR_DB_DIR**: ChromaDB storage directory (default: ./chroma)
- **HOST**: Server host (default: 0.0.0.0)
- **PORT**: Server port (default: 8000)

### Model Parameters
```python
# AI Client Configuration
temperature: float = 0.2      # Response randomness (0.0-2.0)
top_p: float = 1.0           # Nucleus sampling (0.0-1.0)
max_output_tokens: int = 512  # Maximum response length
stop: List[str] = None       # Stop sequences
```

---

## 🚀 Future Roadmap

### Immediate Next Steps (Phase 2-3)
- [ ] Complete vector database integration
- [ ] Implement document upload and processing
- [ ] Add quiz generation feature
- [ ] Create comprehensive test suite

### Medium Term (Phase 4-5)
- [ ] RAG-based question answering
- [ ] Function calling for reminders
- [ ] Study session scheduling
- [ ] Performance analytics dashboard

### Long Term Vision
- [ ] Voice interaction (Whisper + TTS)
- [ ] Mobile app development
- [ ] Notion/Obsidian integration
- [ ] Gamification features
- [ ] Multi-language support

---

## 📞 Support & Contact

**Author**: Chethan Regala  
**Email**: <EMAIL>  
**GitHub**: [chethan-gen](https://github.com/chethan-gen)  
**LinkedIn**: [Chethan Regala](https://www.linkedin.com/in/chethan-regala-9b671a34a/)

**Project Repository**: [Smart-Study-Assistant](https://github.com/kalviumcommunity/Smart-Study-Assistant)

---

*Last Updated: January 2025*
*Version: 0.1.0*
