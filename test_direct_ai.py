#!/usr/bin/env python3
"""Test AI directly with flashcard prompt"""

import sys
import os
sys.path.insert(0, os.getcwd())

from src.core.ai_client import AIClient
from src.core.prompts import PromptFactory

# Create instances
client = AIClient()
pf = PromptFactory()

# Test structured flashcards prompt
topic = "photosynthesis"
count = 2
contexts = []

print("=== Testing Structured Flashcards Prompt ===")
instruction = pf.structured_flashcards(topic=topic, num_cards=count, contexts=contexts)
print("Instruction:")
print(instruction)
print("\n" + "="*50 + "\n")

# Build messages
messages = pf.build_messages(user_instruction=instruction, contexts=contexts)
print("Messages:")
for i, msg in enumerate(messages):
    print(f"Message {i} ({msg['role']}):")
    print(msg['content'][:200] + "..." if len(msg['content']) > 200 else msg['content'])
    print()

print("="*50)
print("Sending to AI...")

# Get response
response = client.chat(messages)
print("AI Response:")
print(response)
print("\n" + "="*50)

# Try to parse JSON
import json
try:
    parsed = json.loads(response)
    print("✓ Successfully parsed JSON:")
    print(json.dumps(parsed, indent=2))
except json.JSONDecodeError as e:
    print(f"✗ JSON parsing failed: {e}")
    print("Raw response:", repr(response))
