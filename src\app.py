from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel
from typing import List, Optional
from config.config import settings
from src.features.flashcard_generator import FlashcardGenerator
from src.core.ai_client import AIClient

app = FastAPI(title="Smart Study Assistant API", version="0.1.0")

# Initialize components
ai_client = AIClient()
flashcard_gen = FlashcardGenerator(ai_client)

class FlashcardRequest(BaseModel):
    topic: str
    context: Optional[str] = None
    count: int = 5

class FlashcardResponse(BaseModel):
    topic: str
    flashcards: List[dict]

@app.get("/health")
def health():
    return {
        "status": "ok",
        "model_chat": settings.MODEL_CHAT,
        "model_embedding": settings.MODEL_EMBEDDING
    }

@app.post("/flashcards", response_model=FlashcardResponse)
def generate_flashcards(request: FlashcardRequest):
    """Generate flashcards for a given topic"""
    try:
        # Generate flashcards using the flashcard generator
        result = flashcard_gen.generate_flashcards(
            topic=request.topic,
            context=request.context,
            count=request.count
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating flashcards: {str(e)}")

@app.get("/flashcards")
def get_flashcards_info():
    """Get information about the flashcards endpoint"""
    return {
        "message": "Use POST method to generate flashcards",
        "endpoint": "/flashcards",
        "method": "POST",
        "example_payload": {
            "topic": "photosynthesis",
            "context": "optional context text",
            "count": 5
        }
    }