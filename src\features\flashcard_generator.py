# Flashcard generator with structured outputs (Phase 3)

from typing import List, Dict, Optional
import json
from src.core.ai_client import AI<PERSON>lient
from src.core.prompts import PromptFactory

class FlashcardGenerator:
    """Generate flashcards from topics and context using AI"""

    def __init__(self, ai_client: AIClient):
        self.ai_client = ai_client
        self.prompt_factory = PromptFactory()

    def generate_flashcards(self, topic: str, context: Optional[str] = None, count: int = 5) -> Dict:
        """
        Generate flashcards for a given topic

        Args:
            topic: The topic to generate flashcards for
            context: Optional context text to base flashcards on
            count: Number of flashcards to generate

        Returns:
            Dictionary containing topic and list of flashcards
        """
        # Prepare the prompt
        user_text = f"Generate {count} flashcards for {topic}."

        # Prepare contexts if provided
        contexts = []
        if context:
            contexts = [{"doc_id": "user_context", "chunk_id": "1", "text": context}]

        # Use structured flashcards prompt directly
        instruction = self.prompt_factory.structured_flashcards(topic=topic, num_cards=count, contexts=contexts)
        messages = self.prompt_factory.build_messages(user_instruction=instruction, contexts=contexts)

        # Get response from AI
        response_text = self.ai_client.chat(messages)

        # Try to parse JSON response, fallback to simple format if needed
        try:
            # Remove markdown code blocks if present
            clean_response = response_text.strip()
            if clean_response.startswith('```json'):
                clean_response = clean_response[7:]  # Remove ```json
            if clean_response.startswith('```'):
                clean_response = clean_response[3:]   # Remove ```
            if clean_response.endswith('```'):
                clean_response = clean_response[:-3]  # Remove trailing ```
            clean_response = clean_response.strip()

            response_data = json.loads(clean_response)
            if isinstance(response_data, dict) and "flashcards" in response_data:
                return response_data
        except json.JSONDecodeError:
            pass

        # Fallback: create a simple structure
        return {
            "topic": topic,
            "flashcards": [
                {
                    "question": f"What is {topic}?",
                    "answer": response_text[:200] + "..." if len(response_text) > 200 else response_text,
                    "source": {"doc_id": "ai_generated", "chunk_id": "1"}
                }
            ]
        }
