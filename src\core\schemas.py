from pydantic import BaseModel, Field
from typing import List, Optional

class Flashcard(BaseModel):
    question: str
    answer: str
    source: Optional[dict] = None

class FlashcardSet(BaseModel):
    topic: str
    flashcards: List[Flashcard]

class QuizQuestion(BaseModel):
    question: str
    options: List[str]
    correct_option: int
    explanation: Optional[str] = None
    source: Optional[dict] = None

class Quiz(BaseModel):
    topic: str
    quiz: List[QuizQuestion]
