#!/usr/bin/env python3
"""Simple script to test the API endpoints"""

import requests
import json

def test_health_endpoint():
    """Test the health endpoint"""
    try:
        response = requests.get('http://localhost:8000/health')
        print("Health endpoint:")
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        print()
    except Exception as e:
        print(f"Error testing health endpoint: {e}")

def test_flashcards_get():
    """Test the GET flashcards endpoint"""
    try:
        response = requests.get('http://localhost:8000/flashcards')
        print("GET /flashcards:")
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        print()
    except Exception as e:
        print(f"Error testing GET flashcards: {e}")

def test_flashcards_post():
    """Test the POST flashcards endpoint"""
    try:
        payload = {
            "topic": "photosynthesis",
            "count": 2
        }
        response = requests.post('http://localhost:8000/flashcards', json=payload)
        print("POST /flashcards:")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            print(f"Response: {json.dumps(response.json(), indent=2)}")
        else:
            print(f"Error response: {response.text}")
        print()
    except Exception as e:
        print(f"Error testing POST flashcards: {e}")

if __name__ == "__main__":
    print("Testing Smart Study Assistant API...")
    print("=" * 50)
    
    test_health_endpoint()
    test_flashcards_get()
    test_flashcards_post()
    
    print("Testing complete!")
