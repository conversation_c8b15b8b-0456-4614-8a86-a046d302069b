# --- Core runtime ---
openai>=1.40.0
fastapi>=0.111.0
uvicorn[standard]>=0.30.0
python-dotenv>=1.0.1
pydantic>=2.7.0
pydantic-settings>=2.10.0

# --- RAG & LLM orchestration ---
langchain>=0.2.0
langchain-openai>=0.1.7

# --- Vector DB ---
chromadb>=0.5.0
# faiss-cpu is optional but recommended (comment out if install fails)
faiss-cpu>=1.8.0

# --- Documents ---
pymupdf>=1.24.0  # for robust PDF parsing

# --- Utils ---
numpy>=1.26.0
tiktoken>=0.7.0

# --- Testing ---
pytest>=8.2.0