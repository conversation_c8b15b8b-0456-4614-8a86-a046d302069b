from typing import List, Dict, Optional
import json
from src.core.ai_client import AI<PERSON><PERSON>
from src.core.prompts import PromptFactory

class FlashcardGenerator:
    """Generate flashcards from topics and context using AI"""

    def __init__(self, ai_client: AIClient):
        self.ai_client = ai_client
        self.prompt_factory = PromptFactory()

    def generate_flashcards(self, topic: str, context: Optional[str] = None, count: int = 5) -> Dict:
        """
        Generate flashcards for a given topic

        Args:
            topic: The topic to generate flashcards for
            context: Optional context text to base flashcards on
            count: Number of flashcards to generate

        Returns:
            Dictionary containing topic and list of flashcards
        """
        # Prepare context chunks if provided
        contexts = []
        if context:
            contexts = [{"doc_id": "user_context", "chunk_id": "1", "text": context}]

        # Build prompt and message payload
        instruction = self.prompt_factory.structured_flashcards(topic=topic, num_cards=count, contexts=contexts)
        messages = self.prompt_factory.build_messages(user_instruction=instruction, contexts=contexts)

        # Get AI response
        response_text = self.ai_client.chat(messages).strip()

        # Clean markdown code block if present
        if response_text.startswith("```json"):
            response_text = response_text[7:]
        elif response_text.startswith("```"):
            response_text = response_text[3:]
        if response_text.endswith("```"):
            response_text = response_text[:-3]
        response_text = response_text.strip()

        # Try parsing structured JSON
        try:
            response_data = json.loads(response_text)
            if isinstance(response_data, dict) and "flashcards" in response_data:
                return response_data
        except json.JSONDecodeError:
            pass

        # Fallback: basic flashcard structure
        return {
            "topic": topic,
            "flashcards": [
                {
                    "question": f"What is {topic}?",
                    "answer": response_text[:200] + "..." if len(response_text) > 200 else response_text,
                    "source": {"doc_id": "ai_generated", "chunk_id": "1"}
                }
            ]
        }
