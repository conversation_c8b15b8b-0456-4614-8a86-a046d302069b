"""
RTFC prompt templates and PromptFactory for Phase 1.2.

Provides:
- DEFAULT_SYSTEM_PROMPT (Role, Task, Format, Context)
- PromptFactory: zero_shot, one_shot, multi_shot, chain_of_thought,
  structured_flashcards, structured_quiz, select_prompt, build_messages
"""
from typing import List, Dict, Any, Optional, Tuple
import textwrap
import json

DEFAULT_SYSTEM_PROMPT = textwrap.dedent("""
You are an expert AI tutor for engineering students. Follow the RTFC framework:
- Role: Expert AI tutor (concise, exam-focused).
- Task: Answer, explain, summarize, or generate study material using the provided context.
- Format: When asked for JSON / flashcards / quizzes output strictly valid JSON matching the schema requested. When asked for text answers be concise and include examples if helpful.
- Context: If a context (notes/doc chunks) is provided, ground answers in that context and, where possible, cite the source using the tag [DOC:doc_id|chunk_id]. If the answer is not present in the context, reply exactly with "NOT_IN_DOCS".
""").strip()

class PromptFactory:
    def __init__(self, system_prompt: str = DEFAULT_SYSTEM_PROMPT, context_truncate_chars: int = 3000):
        self.system_prompt = system_prompt
        self.context_truncate_chars = context_truncate_chars

    def _format_context(self, contexts: Optional[List[Dict[str,str]]]) -> str:
        """contexts: list of dict {'doc_id': str, 'chunk_id': str (optional), 'text': str}"""
        if not contexts:
            return "[NO_CONTEXT_PROVIDED]"
        parts = []
        for i, c in enumerate(contexts):
            doc_id = c.get("doc_id", f"doc{i}")
            chunk_id = c.get("chunk_id", str(i))
            text = c.get("text", "").strip()
            if not text:
                continue
            header = f"[DOC:{doc_id}|CH:{chunk_id}]"
            parts.append(f"{header}\n{text}")
        joined = "\n\n".join(parts)
        if len(joined) > self.context_truncate_chars:
            return joined[: self.context_truncate_chars] + "\n...[TRUNCATED]"
        return joined

    def build_messages(self, user_instruction: str, contexts: Optional[List[Dict[str,str]]] = None, system_extra: Optional[str] = None) -> List[Dict[str, str]]:
        """Construct chat messages for OpenAI chat APIs."""
        system_content = self.system_prompt
        if system_extra:
            system_content = system_content + "\n\n" + system_extra
        ctx_block = self._format_context(contexts)
        user_content = textwrap.dedent(f"""
        ## CONTEXT_START
        {ctx_block}
        ## CONTEXT_END

        ## USER_INSTRUCTION
        {user_instruction}
        """).strip()
        return [
            {"role": "system", "content": system_content},
            {"role": "user", "content": user_content},
        ]

    # -- Prompt styles ---------------------------------------------------
    def zero_shot(self, question: str, contexts: Optional[List[Dict[str,str]]] = None) -> str:
        return textwrap.dedent(f"""
        Answer concisely using only the provided context. If the information is not present in the context, reply exactly with "NOT_IN_DOCS".

        Question:
        {question}

        Answer:
        """).strip()

    def one_shot(self, question: str, example_q: str, example_a: str, contexts: Optional[List[Dict[str,str]]] = None) -> str:
        return textwrap.dedent(f"""
        Example:
        Q: {example_q}
        A: {example_a}

        Now answer the following question concisely using the provided context. If the information is not present, reply "NOT_IN_DOCS".

        Question:
        {question}

        Answer:
        """).strip()

    def multi_shot(self, question: str, examples: List[Tuple[str, str]], contexts: Optional[List[Dict[str,str]]] = None) -> str:
        ex_text = "\n\n".join([f"Q: {q}\nA: {a}" for q, a in examples])
        return textwrap.dedent(f"""
        Examples:
        {ex_text}

        Now answer the following question concisely using the provided context. If the information is not present, reply "NOT_IN_DOCS".

        Question:
        {question}

        Answer:
        """).strip()

    def chain_of_thought(self, problem: str, contexts: Optional[List[Dict[str,str]]] = None, max_steps: int = 10) -> str:
        return textwrap.dedent(f"""
        Solve this step-by-step, showing your reasoning at each stage. Begin with "Step 1:" and conclude with "Final Answer:".

        Problem:
        {problem}

        Let me think through this step by step:
        1. Step 1: ...
        2. Step 2: ...
        ...
        {max_steps}. Final Answer:
        """).strip()

    # -- Structured outputs (JSON) --------------------------------------
    def structured_flashcards(self, topic: str, num_cards: int = 5, contexts: Optional[List[Dict[str,str]]] = None) -> str:
        schema = {
            "type": "object",
            "properties": {
                "topic": {"type": "string"},
                "flashcards": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "question": {"type": "string"},
                            "answer": {"type": "string"},
                            "source": {"type": "object", "properties": {"doc_id": {"type": "string"}, "chunk_id": {"type": "string"}}, "required": ["doc_id"]},
                        },
                        "required": ["question", "answer"]
                    }
                }
            },
            "required": ["topic", "flashcards"]
        }
        return textwrap.dedent(f"""
        Produce exactly valid JSON matching this schema (no extra text):
        {json.dumps(schema)}

        Instructions:
        - Generate {num_cards} flashcards for topic: "{topic}".
        - If context is provided, use it as the primary source. If no context is provided, use your general knowledge.
        - Each flashcard must include "question" and "answer".
        - Where possible include "source" with doc_id and chunk_id indicating where the info came from. If using general knowledge, set source to null.
        - Create educational, exam-focused questions that test understanding of key concepts.
        """).strip()

    def structured_quiz(self, topic: str, num_questions: int = 5, contexts: Optional[List[Dict[str,str]]] = None) -> str:
        schema = {
            "type": "object",
            "properties": {
                "topic": {"type": "string"},
                "quiz": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "question": {"type": "string"},
                            "options": {"type": "array", "items": {"type": "string"}, "minItems": 4, "maxItems": 4},
                            "correct_option": {"type": "integer"},
                            "explanation": {"type": "string"},
                            "source": {"type": "object", "properties": {"doc_id": {"type": "string"}, "chunk_id": {"type": "string"}}}
                        },
                        "required": ["question", "options", "correct_option"]
                    }
                }
            },
            "required": ["topic", "quiz"]
        }
        return textwrap.dedent(f"""
        Produce exactly valid JSON matching this schema (no extra text):
        {json.dumps(schema)}

        Instructions:
        - Generate {num_questions} MCQs for topic: "{topic}" using only the provided context.
        - Provide exactly 4 options per question. Indexing: 0..3 for correct_option.
        - Include a short explanation and source if the answer is present in context.
        - If insufficient context, produce fewer high-quality questions rather than hallucinate.
        """).strip()

    # -- Selector -------------------------------------------------------
    def select_prompt(self, user_text: str, contexts: Optional[List[Dict[str,str]]] = None) -> Tuple[str, List[Dict[str,str]]]:
        """Return (user_instruction_text, contexts) chosen by heuristics"""
        q = (user_text or "").lower()
        # heuristics
        if any(k in q for k in ["flashcard", "flashcards", "make flashcards", "generate flashcards"]):
            # infer topic if provided
            topic = user_text
            return self.structured_flashcards(topic=topic, contexts=contexts), contexts or []
        if any(k in q for k in ["quiz", "mcq", "multiple choice"]):
            topic = user_text
            return self.structured_quiz(topic=topic, contexts=contexts), contexts or []
        if any(k in q for k in ["derive", "prove", "calculate", "step by step", "show your work", "solve"]):
            return self.chain_of_thought(problem=user_text, contexts=contexts), contexts or []
        # default: zero shot
        # short guiding prompt to prefer use-of-context
        zero = self.zero_shot(question=user_text, contexts=contexts)
        return zero, contexts or []
