#!/usr/bin/env python3
"""Debug script for flashcard generation"""

import sys
import os
sys.path.insert(0, os.getcwd())

try:
    from src.features.flashcard_generator import FlashcardGenerator
    from src.core.ai_client import AIClient
    print("✓ Imports successful")
    
    # Test AI client
    client = AIClient()
    print("✓ AI Client created")
    
    # Test simple chat
    messages = [{"role": "user", "content": "Say 'test successful'"}]
    response = client.chat(messages)
    print(f"✓ AI Client test: {response}")
    
    # Test flashcard generator
    gen = FlashcardGenerator(client)
    print("✓ FlashcardGenerator created")
    
    # Test prompt factory
    from src.core.prompts import PromptFactory
    pf = PromptFactory()
    print("✓ PromptFactory created")

    # Test prompt selection
    user_text = "Generate 2 flashcards for photosynthesis."
    instruction, contexts = pf.select_prompt(user_text, contexts=[])
    print(f"✓ Prompt selected: {instruction[:100]}...")

    # Test message building
    messages = pf.build_messages(user_instruction=instruction, contexts=contexts)
    print(f"✓ Messages built: {len(messages)} messages")

    # Test AI response
    response = client.chat(messages)
    print(f"✓ AI Response: {response[:200]}...")

    # Test flashcard generation
    print("Generating flashcards...")
    result = gen.generate_flashcards('photosynthesis', count=2)
    print(f"✓ Flashcards generated: {result}")
    
except Exception as e:
    print(f"✗ Error: {e}")
    import traceback
    traceback.print_exc()
